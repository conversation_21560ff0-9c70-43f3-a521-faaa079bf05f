<?php

namespace App\Livewire;

use App\Models\Company;
use App\Models\UserConfig;
use Livewire\Component;
use Illuminate\Support\Facades\Auth;

class AdminDashboard extends Component
{
    public $totalCompanies;
    public $activeCompanies;
    public $totalConfigs;
    public $recentCompanies;

    public function mount()
    {
        $user = Auth::user();

        $this->totalCompanies = $user->companies()->count();
        $this->activeCompanies = $user->companies()->active()->count();
        $this->totalConfigs = $user->configs()->where('is_active', true)->count();
        $this->recentCompanies = $user->companies()
            ->latest()
            ->take(5)
            ->get();
    }

    public function render()
    {
        return view('livewire.admin-dashboard')
            ->layout('layouts.admin')
            ->section('page-title', 'Dashboard');
    }
}
