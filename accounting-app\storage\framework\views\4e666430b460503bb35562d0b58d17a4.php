<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['routes', 'label']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['routes', 'label']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
    $isActive = false;
    foreach ($routes as $route) {
        if (request()->routeIs($route)) {
            $isActive = true;
            break;
        }
    }
?>

<div x-data="{ open: <?php echo e($isActive ? 'true' : 'false'); ?> }" class="ml-4">
    <button @click="open = !open" 
            class="flex items-center justify-between w-full px-4 py-2 text-sm font-medium text-gray-400 rounded-lg hover:bg-gray-700 hover:text-white <?php echo e($isActive ? 'bg-gray-700 text-white' : ''); ?>">
        <div class="flex items-center">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
            <?php echo e($label); ?>

        </div>
        <svg class="w-3 h-3 transition-transform" :class="{ 'rotate-180': open }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
        </svg>
    </button>
    <div x-show="open" x-transition class="ml-6 mt-1 space-y-1">
        <?php echo e($slot); ?>

    </div>
</div>
<?php /**PATH D:\php_projects\accounting-app\resources\views/components/admin/sidebar/sub-menu-group.blade.php ENDPATH**/ ?>