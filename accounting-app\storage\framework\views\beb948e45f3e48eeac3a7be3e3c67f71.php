<div class="p-6">
    <div class="max-w-7xl mx-auto">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6 text-gray-900">
                <!-- Header -->
                <div class="flex justify-between items-center mb-6">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">User Configuration Management</h1>
                        <p class="mt-2 text-gray-600">Manage your personal settings and preferences</p>
                    </div>
                    <button wire:click="openModal" class="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded-lg flex items-center">
                        <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Add Configuration
                    </button>
                </div>

                <!-- Flash Messages -->
                <!--[if BLOCK]><![endif]--><?php if(session()->has('message')): ?>
                    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                        <?php echo e(session('message')); ?>

                    </div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                <!-- Search -->
                <div class="mb-6">
                    <div class="relative">
                        <input wire:model.live="search" type="text" placeholder="Search configurations..."
                               class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-purple-500 focus:border-purple-500">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- Configurations Table -->
                <div class="bg-white shadow overflow-hidden sm:rounded-md">
                    <ul class="divide-y divide-gray-200">
                        <!--[if BLOCK]><![endif]--><?php $__empty_1 = true; $__currentLoopData = $configs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $config): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <li class="px-6 py-4 hover:bg-gray-50">
                                <div class="flex items-center justify-between">
                                    <div class="flex-1 min-w-0">
                                        <div class="flex items-center">
                                            <div class="flex-1">
                                                <div class="flex items-center space-x-3">
                                                    <p class="text-lg font-medium text-gray-900 truncate"><?php echo e($config->config_key); ?></p>
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                        <?php echo e($config->config_type === 'string' ? 'bg-blue-100 text-blue-800' : ''); ?>

                                                        <?php echo e($config->config_type === 'json' ? 'bg-green-100 text-green-800' : ''); ?>

                                                        <?php echo e($config->config_type === 'boolean' ? 'bg-yellow-100 text-yellow-800' : ''); ?>

                                                        <?php echo e($config->config_type === 'integer' ? 'bg-purple-100 text-purple-800' : ''); ?>">
                                                        <?php echo e(ucfirst($config->config_type)); ?>

                                                    </span>
                                                    <button wire:click="toggleActive(<?php echo e($config->id); ?>)"
                                                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium cursor-pointer
                                                            <?php echo e($config->is_active ? 'bg-green-100 text-green-800 hover:bg-green-200' : 'bg-red-100 text-red-800 hover:bg-red-200'); ?>">
                                                        <?php echo e($config->is_active ? 'Active' : 'Inactive'); ?>

                                                    </button>
                                                </div>
                                                <!--[if BLOCK]><![endif]--><?php if($config->description): ?>
                                                    <p class="text-sm text-gray-500 mt-1"><?php echo e($config->description); ?></p>
                                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                <div class="mt-2">
                                                    <p class="text-sm text-gray-600">
                                                        <span class="font-medium">Value:</span>
                                                        <!--[if BLOCK]><![endif]--><?php if($config->config_type === 'json'): ?>
                                                            <code class="bg-gray-100 px-2 py-1 rounded text-xs"><?php echo e(Str::limit($config->config_value, 100)); ?></code>
                                                        <?php elseif($config->config_type === 'boolean'): ?>
                                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium <?php echo e($config->config_value ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'); ?>">
                                                                <?php echo e($config->config_value ? 'True' : 'False'); ?>

                                                            </span>
                                                        <?php else: ?>
                                                            <span class="font-mono"><?php echo e(Str::limit($config->config_value, 100)); ?></span>
                                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                    </p>
                                                </div>
                                                <div class="mt-1 text-xs text-gray-400">
                                                    Created <?php echo e($config->created_at->diffForHumans()); ?>

                                                    <!--[if BLOCK]><![endif]--><?php if($config->updated_at != $config->created_at): ?>
                                                        • Updated <?php echo e($config->updated_at->diffForHumans()); ?>

                                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-2 ml-4">
                                        <button wire:click="editConfig(<?php echo e($config->id); ?>)"
                                                class="text-purple-600 hover:text-purple-900 p-2 rounded-lg hover:bg-purple-50">
                                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                            </svg>
                                        </button>
                                        <button wire:click="deleteConfig(<?php echo e($config->id); ?>)"
                                                onclick="return confirm('Are you sure you want to delete this configuration?')"
                                                class="text-red-600 hover:text-red-900 p-2 rounded-lg hover:bg-red-50">
                                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <li class="px-6 py-12 text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                <h3 class="mt-2 text-sm font-medium text-gray-900">No configurations</h3>
                                <p class="mt-1 text-sm text-gray-500">Get started by creating a new configuration.</p>
                                <div class="mt-6">
                                    <button wire:click="openModal" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700">
                                        <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                        New Configuration
                                    </button>
                                </div>
                            </li>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    </ul>
                </div>

                <!-- Pagination -->
                <!--[if BLOCK]><![endif]--><?php if($configs->hasPages()): ?>
                    <div class="mt-6">
                        <?php echo e($configs->links()); ?>

                    </div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </div>
        </div>
    </div>

    <!-- Modal -->
    <!--[if BLOCK]><![endif]--><?php if($showModal): ?>
        <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" wire:click="closeModal">
            <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-2/3 lg:w-1/2 shadow-lg rounded-md bg-white" wire:click.stop>
                <div class="mt-3">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-900">
                            <?php echo e($editingConfig ? 'Edit Configuration' : 'Add New Configuration'); ?>

                        </h3>
                        <button wire:click="closeModal" class="text-gray-400 hover:text-gray-600">
                            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    <form wire:submit.prevent="saveConfig" class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Configuration Key -->
                            <div>
                                <label for="config_key" class="block text-sm font-medium text-gray-700">Configuration Key *</label>
                                <input wire:model="config_key" type="text" id="config_key"
                                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
                                       placeholder="e.g., default_currency, theme_preference">
                                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['config_key'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-red-500 text-sm"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                            </div>

                            <!-- Configuration Type -->
                            <div>
                                <label for="config_type" class="block text-sm font-medium text-gray-700">Type *</label>
                                <select wire:model="config_type" id="config_type"
                                        class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500">
                                    <option value="string">String</option>
                                    <option value="integer">Integer</option>
                                    <option value="boolean">Boolean</option>
                                    <option value="json">JSON</option>
                                </select>
                                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['config_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-red-500 text-sm"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                        </div>

                        <!-- Configuration Value -->
                        <div>
                            <label for="config_value" class="block text-sm font-medium text-gray-700">Value</label>
                            <!--[if BLOCK]><![endif]--><?php if($config_type === 'boolean'): ?>
                                <div class="mt-1">
                                    <label class="inline-flex items-center">
                                        <input wire:model="config_value" type="radio" value="1" name="boolean_value"
                                               class="form-radio h-4 w-4 text-purple-600">
                                        <span class="ml-2">True</span>
                                    </label>
                                    <label class="inline-flex items-center ml-6">
                                        <input wire:model="config_value" type="radio" value="0" name="boolean_value"
                                               class="form-radio h-4 w-4 text-purple-600">
                                        <span class="ml-2">False</span>
                                    </label>
                                </div>
                            <?php elseif($config_type === 'json'): ?>
                                <textarea wire:model="config_value" id="config_value" rows="4"
                                          class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
                                          placeholder='{"key": "value", "array": [1, 2, 3]}'></textarea>
                                <p class="mt-1 text-sm text-gray-500">Enter valid JSON format</p>
                            <?php else: ?>
                                <input wire:model="config_value" type="<?php echo e($config_type === 'integer' ? 'number' : 'text'); ?>" id="config_value"
                                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
                                       placeholder="<?php echo e($config_type === 'integer' ? 'Enter a number' : 'Enter text value'); ?>">
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['config_value'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-red-500 text-sm"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                        </div>

                        <!-- Description -->
                        <div>
                            <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                            <textarea wire:model="description" id="description" rows="3"
                                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
                                      placeholder="Describe what this configuration is used for..."></textarea>
                            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-red-500 text-sm"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                        </div>

                        <!-- Active Status -->
                        <div class="flex items-center">
                            <input wire:model="is_active" type="checkbox" id="is_active"
                                   class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                            <label for="is_active" class="ml-2 block text-sm text-gray-900">Active</label>
                        </div>

                        <!-- Form Actions -->
                        <div class="flex justify-end space-x-3 pt-6 border-t">
                            <button type="button" wire:click="closeModal"
                                    class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
                                Cancel
                            </button>
                            <button type="submit"
                                    class="bg-purple-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
                                <?php echo e($editingConfig ? 'Update Configuration' : 'Create Configuration'); ?>

                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
</div>
<?php /**PATH D:\php_projects\accounting-app\resources\views/livewire/user-config-manager.blade.php ENDPATH**/ ?>