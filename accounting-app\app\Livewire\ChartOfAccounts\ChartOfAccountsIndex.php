<?php

namespace App\Livewire\ChartOfAccounts;

use App\Models\ChartOfAccount;
use App\Models\Company;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Auth;

class ChartOfAccountsIndex extends Component
{
    use WithPagination;

    public $search = '';
    public $showModal = false;
    public $editingAccount = null;
    public $selectedCompany = '';
    public $selectedAccountType = '';

    // Form fields
    public $company_id = '';
    public $account_code = '';
    public $account_name = '';
    public $account_type = '';
    public $account_subtype = '';
    public $description = '';
    public $opening_balance = 0;
    public $is_active = true;
    public $parent_account_id = '';

    protected $rules = [
        'company_id' => 'required|exists:companies,id',
        'account_code' => 'required|string|max:20',
        'account_name' => 'required|string|max:255',
        'account_type' => 'required|in:asset,liability,equity,income,expense',
        'account_subtype' => 'nullable|string',
        'description' => 'nullable|string',
        'opening_balance' => 'nullable|numeric',
        'is_active' => 'boolean',
        'parent_account_id' => 'nullable|exists:chart_of_accounts,id',
    ];

    public function openModal()
    {
        $this->resetForm();
        $this->showModal = true;
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->editingAccount = null;
        $this->resetForm();
        $this->resetValidation();
    }

    public function editAccount($accountId)
    {
        $account = ChartOfAccount::with('company')->findOrFail($accountId);

        // Check if user owns the company
        if ($account->company->user_id !== Auth::id()) {
            abort(403);
        }

        $this->editingAccount = $account;
        $this->company_id = $account->company_id;
        $this->account_code = $account->account_code;
        $this->account_name = $account->account_name;
        $this->account_type = $account->account_type;
        $this->account_subtype = $account->account_subtype;
        $this->description = $account->description;
        $this->opening_balance = $account->opening_balance;
        $this->is_active = $account->is_active;
        $this->parent_account_id = $account->parent_account_id;

        $this->showModal = true;
    }

    public function saveAccount()
    {
        $this->validate();

        // Verify user owns the company
        $company = Auth::user()->companies()->findOrFail($this->company_id);

        $data = [
            'company_id' => $this->company_id,
            'account_code' => $this->account_code,
            'account_name' => $this->account_name,
            'account_type' => $this->account_type,
            'account_subtype' => $this->account_subtype,
            'description' => $this->description,
            'opening_balance' => $this->opening_balance ?: 0,
            'current_balance' => $this->opening_balance ?: 0,
            'is_active' => $this->is_active,
            'parent_account_id' => $this->parent_account_id ?: null,
        ];

        if ($this->editingAccount) {
            $this->editingAccount->update($data);
            session()->flash('message', 'Account updated successfully.');
        } else {
            ChartOfAccount::create($data);
            session()->flash('message', 'Account created successfully.');
        }

        $this->closeModal();
    }

    public function deleteAccount($accountId)
    {
        $account = ChartOfAccount::with('company')->findOrFail($accountId);

        // Check if user owns the company
        if ($account->company->user_id !== Auth::id()) {
            abort(403);
        }

        $account->delete();
        session()->flash('message', 'Account deleted successfully.');
    }

    public function toggleActive($accountId)
    {
        $account = ChartOfAccount::with('company')->findOrFail($accountId);

        // Check if user owns the company
        if ($account->company->user_id !== Auth::id()) {
            abort(403);
        }

        $account->update(['is_active' => !$account->is_active]);
        session()->flash('message', 'Account status updated.');
    }

    private function resetForm()
    {
        $this->company_id = '';
        $this->account_code = '';
        $this->account_name = '';
        $this->account_type = '';
        $this->account_subtype = '';
        $this->description = '';
        $this->opening_balance = 0;
        $this->is_active = true;
        $this->parent_account_id = '';
    }

    public function render()
    {
        $companies = Auth::user()->companies()->active()->get();

        $chartOfAccounts = ChartOfAccount::with('company', 'parentAccount')
            ->whereHas('company', function ($query) {
                $query->where('user_id', Auth::id());
            })
            ->when($this->search, function ($query) {
                $query->where(function ($q) {
                    $q->where('account_code', 'like', '%' . $this->search . '%')
                      ->orWhere('account_name', 'like', '%' . $this->search . '%')
                      ->orWhereHas('company', function ($companyQuery) {
                          $companyQuery->where('name', 'like', '%' . $this->search . '%');
                      });
                });
            })
            ->when($this->selectedCompany, function ($query) {
                $query->where('company_id', $this->selectedCompany);
            })
            ->when($this->selectedAccountType, function ($query) {
                $query->where('account_type', $this->selectedAccountType);
            })
            ->orderBy('account_type')
            ->orderBy('account_code')
            ->paginate(15);

        return view('livewire.chart-of-accounts.chart-of-accounts-index', compact('chartOfAccounts', 'companies'))
            ->layout('layouts.admin')
            ->section('page-title', 'Chart of Accounts');
    }
}
