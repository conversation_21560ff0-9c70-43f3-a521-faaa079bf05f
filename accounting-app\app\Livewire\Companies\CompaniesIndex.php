<?php

namespace App\Livewire\Companies;

use App\Models\Company;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Auth;

class CompaniesIndex extends Component
{
    use WithPagination;

    public $search = '';
    public $showModal = false;
    public $editingCompany = null;

    // Form fields
    public $name = '';
    public $is_active = true;

    protected $rules = [
        'name' => 'required|string|max:255',
        'is_active' => 'boolean',
    ];

    public function openModal()
    {
        $this->resetForm();
        $this->showModal = true;
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->editingCompany = null;
        $this->resetForm();
        $this->resetValidation();
    }

    public function editCompany($companyId)
    {
        $company = Auth::user()->companies()->findOrFail($companyId);
        $this->editingCompany = $company;

        $this->name = $company->name;
        $this->is_active = $company->is_active;

        $this->showModal = true;
    }

    public function saveCompany()
    {
        $this->validate();

        $data = [
            'user_id' => Auth::id(),
            'name' => $this->name,
            'is_active' => $this->is_active,
        ];

        if ($this->editingCompany) {
            $this->editingCompany->update($data);
            session()->flash('message', 'Company updated successfully.');
        } else {
            Company::create($data);
            session()->flash('message', 'Company created successfully.');
        }

        $this->closeModal();
    }

    public function deleteCompany($companyId)
    {
        $company = Auth::user()->companies()->findOrFail($companyId);
        $company->delete();

        session()->flash('message', 'Company deleted successfully.');
    }

    public function toggleActive($companyId)
    {
        $company = Auth::user()->companies()->findOrFail($companyId);
        $company->update(['is_active' => !$company->is_active]);

        session()->flash('message', 'Company status updated.');
    }

    private function resetForm()
    {
        $this->name = '';
        $this->is_active = true;
    }

    public function render()
    {
        $companies = Auth::user()->companies()
            ->when($this->search, function ($query) {
                $query->where('name', 'like', '%' . $this->search . '%');
            })
            ->latest()
            ->paginate(10);

        return view('livewire.companies.companies-index', compact('companies'))
            ->layout('layouts.admin')
            ->section('page-title', 'Companies');
    }
}
