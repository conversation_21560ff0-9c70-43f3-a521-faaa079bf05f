<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Company;
use App\Models\FinancialYear;
use App\Models\ChartOfAccount;
use Illuminate\Support\Facades\Hash;

class AccountingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        $user = User::firstOrCreate([
            'email' => '<EMAIL>'
        ], [
            'name' => 'Admin User',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
        ]);

        // Create sample companies
        $company1 = Company::firstOrCreate([
            'user_id' => $user->id,
            'name' => 'ABC Corporation'
        ], [
            'is_active' => true,
        ]);

        $company2 = Company::firstOrCreate([
            'user_id' => $user->id,
            'name' => 'XYZ Ltd'
        ], [
            'is_active' => true,
        ]);

        // Create financial years
        FinancialYear::firstOrCreate([
            'company_id' => $company1->id,
            'name' => 'FY 2024-2025'
        ], [
            'start_date' => '2024-04-01',
            'end_date' => '2025-03-31',
            'is_active' => true,
            'is_current' => true,
        ]);

        FinancialYear::firstOrCreate([
            'company_id' => $company2->id,
            'name' => 'FY 2024-2025'
        ], [
            'start_date' => '2024-01-01',
            'end_date' => '2024-12-31',
            'is_active' => true,
            'is_current' => true,
        ]);

        // Create default chart of accounts for company1
        $this->createDefaultAccounts($company1->id);
        $this->createDefaultAccounts($company2->id);
    }

    private function createDefaultAccounts($companyId)
    {
        $accounts = [
            // Assets
            ['1000', 'Cash', 'asset', 'current_asset', 'Cash in hand and bank'],
            ['1100', 'Accounts Receivable', 'asset', 'current_asset', 'Money owed by customers'],
            ['1200', 'Inventory', 'asset', 'current_asset', 'Stock of goods'],
            ['1500', 'Equipment', 'asset', 'fixed_asset', 'Office equipment and machinery'],
            ['1600', 'Building', 'asset', 'fixed_asset', 'Office building and property'],

            // Liabilities
            ['2000', 'Accounts Payable', 'liability', 'current_liability', 'Money owed to suppliers'],
            ['2100', 'Short-term Loans', 'liability', 'current_liability', 'Loans payable within a year'],
            ['2500', 'Long-term Loans', 'liability', 'long_term_liability', 'Loans payable after a year'],

            // Equity
            ['3000', 'Owner Equity', 'equity', 'owner_equity', 'Owner\'s investment in business'],
            ['3100', 'Retained Earnings', 'equity', 'retained_earnings', 'Accumulated profits'],

            // Income
            ['4000', 'Sales Revenue', 'income', 'operating_income', 'Revenue from sales'],
            ['4100', 'Service Revenue', 'income', 'operating_income', 'Revenue from services'],
            ['4900', 'Other Income', 'income', 'other_income', 'Miscellaneous income'],

            // Expenses
            ['5000', 'Cost of Goods Sold', 'expense', 'operating_expense', 'Direct cost of products sold'],
            ['5100', 'Salaries Expense', 'expense', 'operating_expense', 'Employee salaries and wages'],
            ['5200', 'Rent Expense', 'expense', 'operating_expense', 'Office rent'],
            ['5300', 'Utilities Expense', 'expense', 'operating_expense', 'Electricity, water, internet'],
            ['5400', 'Marketing Expense', 'expense', 'operating_expense', 'Advertising and promotion'],
            ['5900', 'Other Expenses', 'expense', 'other_expense', 'Miscellaneous expenses'],
        ];

        foreach ($accounts as $account) {
            ChartOfAccount::firstOrCreate([
                'company_id' => $companyId,
                'account_code' => $account[0]
            ], [
                'account_name' => $account[1],
                'account_type' => $account[2],
                'account_subtype' => $account[3],
                'description' => $account[4],
                'opening_balance' => 0,
                'current_balance' => 0,
                'is_active' => true,
                'is_system_account' => true,
            ]);
        }
    }
}
