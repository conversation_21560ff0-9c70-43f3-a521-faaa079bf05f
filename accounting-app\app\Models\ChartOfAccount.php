<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ChartOfAccount extends Model
{
    protected $fillable = [
        'company_id',
        'account_code',
        'account_name',
        'account_type',
        'account_subtype',
        'description',
        'opening_balance',
        'current_balance',
        'is_active',
        'is_system_account',
        'parent_account_id',
        'sort_order',
    ];

    protected $casts = [
        'opening_balance' => 'decimal:2',
        'current_balance' => 'decimal:2',
        'is_active' => 'boolean',
        'is_system_account' => 'boolean',
    ];

    public static $accountTypes = [
        'asset' => 'Asset',
        'liability' => 'Liability',
        'equity' => 'Equity',
        'income' => 'Income',
        'expense' => 'Expense',
    ];

    public static $accountSubtypes = [
        'current_asset' => 'Current Asset',
        'fixed_asset' => 'Fixed Asset',
        'other_asset' => 'Other Asset',
        'current_liability' => 'Current Liability',
        'long_term_liability' => 'Long Term Liability',
        'other_liability' => 'Other Liability',
        'owner_equity' => 'Owner Equity',
        'retained_earnings' => 'Retained Earnings',
        'operating_income' => 'Operating Income',
        'other_income' => 'Other Income',
        'operating_expense' => 'Operating Expense',
        'other_expense' => 'Other Expense',
    ];

    /**
     * Get the company that owns the account.
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * Get the parent account.
     */
    public function parentAccount(): BelongsTo
    {
        return $this->belongsTo(ChartOfAccount::class, 'parent_account_id');
    }

    /**
     * Get the child accounts.
     */
    public function childAccounts(): HasMany
    {
        return $this->hasMany(ChartOfAccount::class, 'parent_account_id');
    }

    /**
     * Scope to get active accounts.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get accounts by type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('account_type', $type);
    }

    /**
     * Scope to get accounts for a specific company.
     */
    public function scopeForCompany($query, $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    /**
     * Get formatted account type.
     */
    public function getFormattedAccountTypeAttribute()
    {
        return self::$accountTypes[$this->account_type] ?? $this->account_type;
    }

    /**
     * Get formatted account subtype.
     */
    public function getFormattedAccountSubtypeAttribute()
    {
        return self::$accountSubtypes[$this->account_subtype] ?? $this->account_subtype;
    }

    /**
     * Get the full account display name.
     */
    public function getFullAccountNameAttribute()
    {
        return $this->account_code . ' - ' . $this->account_name;
    }
}
