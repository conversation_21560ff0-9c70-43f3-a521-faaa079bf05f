<!-- Sidebar -->
<div class="w-64 bg-gray-900 shadow-lg" x-data="{ open: true }">
    <div class="flex flex-col h-full">
        <!-- Logo -->
        <?php if (isset($component)) { $__componentOriginal96b050c6298ab984220de38886d969a3 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal96b050c6298ab984220de38886d969a3 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.sidebar.logo','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin.sidebar.logo'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal96b050c6298ab984220de38886d969a3)): ?>
<?php $attributes = $__attributesOriginal96b050c6298ab984220de38886d969a3; ?>
<?php unset($__attributesOriginal96b050c6298ab984220de38886d969a3); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal96b050c6298ab984220de38886d969a3)): ?>
<?php $component = $__componentOriginal96b050c6298ab984220de38886d969a3; ?>
<?php unset($__componentOriginal96b050c6298ab984220de38886d969a3); ?>
<?php endif; ?>

        <!-- Navigation -->
        <nav class="flex-1 px-4 py-6 space-y-2">
            <!-- Dashboard -->
            <?php if (isset($component)) { $__componentOriginal62ac75a5d7dfe7e37f4add8c7ba2a897 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal62ac75a5d7dfe7e37f4add8c7ba2a897 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.sidebar.menu-item','data' => ['route' => 'admin.dashboard','icon' => 'dashboard','label' => 'Dashboard']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin.sidebar.menu-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['route' => 'admin.dashboard','icon' => 'dashboard','label' => 'Dashboard']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal62ac75a5d7dfe7e37f4add8c7ba2a897)): ?>
<?php $attributes = $__attributesOriginal62ac75a5d7dfe7e37f4add8c7ba2a897; ?>
<?php unset($__attributesOriginal62ac75a5d7dfe7e37f4add8c7ba2a897); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal62ac75a5d7dfe7e37f4add8c7ba2a897)): ?>
<?php $component = $__componentOriginal62ac75a5d7dfe7e37f4add8c7ba2a897; ?>
<?php unset($__componentOriginal62ac75a5d7dfe7e37f4add8c7ba2a897); ?>
<?php endif; ?>

            <!-- Accounting -->
            <?php if (isset($component)) { $__componentOriginalb286f25798f92788d9ceb3f142f39088 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb286f25798f92788d9ceb3f142f39088 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.sidebar.menu-group','data' => ['routes' => ['admin.companies*', 'admin.accounting*', 'admin.chart-of-accounts*'],'icon' => 'accounting','label' => 'Accounting']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin.sidebar.menu-group'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['routes' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(['admin.companies*', 'admin.accounting*', 'admin.chart-of-accounts*']),'icon' => 'accounting','label' => 'Accounting']); ?>
                <?php if (isset($component)) { $__componentOriginal59031e45b59667b8490c16f94e418058 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal59031e45b59667b8490c16f94e418058 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.sidebar.sub-menu-item','data' => ['route' => 'admin.companies','label' => 'Companies']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin.sidebar.sub-menu-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['route' => 'admin.companies','label' => 'Companies']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal59031e45b59667b8490c16f94e418058)): ?>
<?php $attributes = $__attributesOriginal59031e45b59667b8490c16f94e418058; ?>
<?php unset($__attributesOriginal59031e45b59667b8490c16f94e418058); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal59031e45b59667b8490c16f94e418058)): ?>
<?php $component = $__componentOriginal59031e45b59667b8490c16f94e418058; ?>
<?php unset($__componentOriginal59031e45b59667b8490c16f94e418058); ?>
<?php endif; ?>
                <?php if (isset($component)) { $__componentOriginal59031e45b59667b8490c16f94e418058 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal59031e45b59667b8490c16f94e418058 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.sidebar.sub-menu-item','data' => ['route' => 'admin.accounting.financial-years','label' => 'Financial Years']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin.sidebar.sub-menu-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['route' => 'admin.accounting.financial-years','label' => 'Financial Years']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal59031e45b59667b8490c16f94e418058)): ?>
<?php $attributes = $__attributesOriginal59031e45b59667b8490c16f94e418058; ?>
<?php unset($__attributesOriginal59031e45b59667b8490c16f94e418058); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal59031e45b59667b8490c16f94e418058)): ?>
<?php $component = $__componentOriginal59031e45b59667b8490c16f94e418058; ?>
<?php unset($__componentOriginal59031e45b59667b8490c16f94e418058); ?>
<?php endif; ?>
                <?php if (isset($component)) { $__componentOriginal59031e45b59667b8490c16f94e418058 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal59031e45b59667b8490c16f94e418058 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.sidebar.sub-menu-item','data' => ['route' => 'admin.chart-of-accounts','label' => 'Chart of Accounts']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin.sidebar.sub-menu-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['route' => 'admin.chart-of-accounts','label' => 'Chart of Accounts']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal59031e45b59667b8490c16f94e418058)): ?>
<?php $attributes = $__attributesOriginal59031e45b59667b8490c16f94e418058; ?>
<?php unset($__attributesOriginal59031e45b59667b8490c16f94e418058); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal59031e45b59667b8490c16f94e418058)): ?>
<?php $component = $__componentOriginal59031e45b59667b8490c16f94e418058; ?>
<?php unset($__componentOriginal59031e45b59667b8490c16f94e418058); ?>
<?php endif; ?>
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb286f25798f92788d9ceb3f142f39088)): ?>
<?php $attributes = $__attributesOriginalb286f25798f92788d9ceb3f142f39088; ?>
<?php unset($__attributesOriginalb286f25798f92788d9ceb3f142f39088); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb286f25798f92788d9ceb3f142f39088)): ?>
<?php $component = $__componentOriginalb286f25798f92788d9ceb3f142f39088; ?>
<?php unset($__componentOriginalb286f25798f92788d9ceb3f142f39088); ?>
<?php endif; ?>

            <!-- Settings -->
            <?php if (isset($component)) { $__componentOriginalb286f25798f92788d9ceb3f142f39088 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb286f25798f92788d9ceb3f142f39088 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.sidebar.menu-group','data' => ['routes' => ['admin.configs*'],'icon' => 'settings','label' => 'Settings']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin.sidebar.menu-group'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['routes' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(['admin.configs*']),'icon' => 'settings','label' => 'Settings']); ?>
                <?php if (isset($component)) { $__componentOriginal59031e45b59667b8490c16f94e418058 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal59031e45b59667b8490c16f94e418058 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.sidebar.sub-menu-item','data' => ['route' => 'admin.configs','label' => 'User Settings']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin.sidebar.sub-menu-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['route' => 'admin.configs','label' => 'User Settings']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal59031e45b59667b8490c16f94e418058)): ?>
<?php $attributes = $__attributesOriginal59031e45b59667b8490c16f94e418058; ?>
<?php unset($__attributesOriginal59031e45b59667b8490c16f94e418058); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal59031e45b59667b8490c16f94e418058)): ?>
<?php $component = $__componentOriginal59031e45b59667b8490c16f94e418058; ?>
<?php unset($__componentOriginal59031e45b59667b8490c16f94e418058); ?>
<?php endif; ?>
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb286f25798f92788d9ceb3f142f39088)): ?>
<?php $attributes = $__attributesOriginalb286f25798f92788d9ceb3f142f39088; ?>
<?php unset($__attributesOriginalb286f25798f92788d9ceb3f142f39088); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb286f25798f92788d9ceb3f142f39088)): ?>
<?php $component = $__componentOriginalb286f25798f92788d9ceb3f142f39088; ?>
<?php unset($__componentOriginalb286f25798f92788d9ceb3f142f39088); ?>
<?php endif; ?>
        </nav>

        <!-- User Menu -->
        <?php if (isset($component)) { $__componentOriginal28c8d049d3979ee4f25d3047ec66d21f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal28c8d049d3979ee4f25d3047ec66d21f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.sidebar.user-menu','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin.sidebar.user-menu'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal28c8d049d3979ee4f25d3047ec66d21f)): ?>
<?php $attributes = $__attributesOriginal28c8d049d3979ee4f25d3047ec66d21f; ?>
<?php unset($__attributesOriginal28c8d049d3979ee4f25d3047ec66d21f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal28c8d049d3979ee4f25d3047ec66d21f)): ?>
<?php $component = $__componentOriginal28c8d049d3979ee4f25d3047ec66d21f; ?>
<?php unset($__componentOriginal28c8d049d3979ee4f25d3047ec66d21f); ?>
<?php endif; ?>
    </div>
</div>
<?php /**PATH D:\php_projects\accounting-app\resources\views/components/admin/sidebar.blade.php ENDPATH**/ ?>