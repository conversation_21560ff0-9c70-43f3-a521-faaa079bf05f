<?php

namespace App\Livewire\Accounting;

use App\Models\FinancialYear;
use App\Models\Company;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Auth;

class FinancialYears extends Component
{
    use WithPagination;

    public $search = '';
    public $showModal = false;
    public $editingYear = null;
    public $selectedCompany = '';

    // Form fields
    public $company_id = '';
    public $name = '';
    public $start_date = '';
    public $end_date = '';
    public $is_active = true;

    protected $rules = [
        'company_id' => 'required|exists:companies,id',
        'name' => 'required|string|max:255',
        'start_date' => 'required|date',
        'end_date' => 'required|date|after:start_date',
        'is_active' => 'boolean',
    ];

    public function openModal()
    {
        $this->resetForm();
        $this->showModal = true;
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->editingYear = null;
        $this->resetForm();
        $this->resetValidation();
    }

    public function editYear($yearId)
    {
        $year = FinancialYear::with('company')->findOrFail($yearId);

        // Check if user owns the company
        if ($year->company->user_id !== Auth::id()) {
            abort(403);
        }

        $this->editingYear = $year;
        $this->company_id = $year->company_id;
        $this->name = $year->name;
        $this->start_date = $year->start_date->format('Y-m-d');
        $this->end_date = $year->end_date->format('Y-m-d');
        $this->is_active = $year->is_active;

        $this->showModal = true;
    }

    public function saveYear()
    {
        $this->validate();

        // Verify user owns the company
        $company = Auth::user()->companies()->findOrFail($this->company_id);

        $data = [
            'company_id' => $this->company_id,
            'name' => $this->name,
            'start_date' => $this->start_date,
            'end_date' => $this->end_date,
            'is_active' => $this->is_active,
        ];

        if ($this->editingYear) {
            $this->editingYear->update($data);
            session()->flash('message', 'Financial year updated successfully.');
        } else {
            FinancialYear::create($data);
            session()->flash('message', 'Financial year created successfully.');
        }

        $this->closeModal();
    }

    public function deleteYear($yearId)
    {
        $year = FinancialYear::with('company')->findOrFail($yearId);

        // Check if user owns the company
        if ($year->company->user_id !== Auth::id()) {
            abort(403);
        }

        $year->delete();
        session()->flash('message', 'Financial year deleted successfully.');
    }

    public function toggleActive($yearId)
    {
        $year = FinancialYear::with('company')->findOrFail($yearId);

        // Check if user owns the company
        if ($year->company->user_id !== Auth::id()) {
            abort(403);
        }

        $year->update(['is_active' => !$year->is_active]);
        session()->flash('message', 'Financial year status updated.');
    }

    public function setAsCurrent($yearId)
    {
        $year = FinancialYear::with('company')->findOrFail($yearId);

        // Check if user owns the company
        if ($year->company->user_id !== Auth::id()) {
            abort(403);
        }

        $year->setAsCurrent();
        session()->flash('message', 'Financial year set as current.');
    }

    private function resetForm()
    {
        $this->company_id = '';
        $this->name = '';
        $this->start_date = '';
        $this->end_date = '';
        $this->is_active = true;
    }

    public function render()
    {
        $companies = Auth::user()->companies()->active()->get();

        $financialYears = FinancialYear::with('company')
            ->whereHas('company', function ($query) {
                $query->where('user_id', Auth::id());
            })
            ->when($this->search, function ($query) {
                $query->where('name', 'like', '%' . $this->search . '%')
                      ->orWhereHas('company', function ($q) {
                          $q->where('name', 'like', '%' . $this->search . '%');
                      });
            })
            ->when($this->selectedCompany, function ($query) {
                $query->where('company_id', $this->selectedCompany);
            })
            ->latest()
            ->paginate(10);

        return view('livewire.accounting.financial-years', compact('financialYears', 'companies'))
            ->layout('layouts.admin')
            ->section('page-title', 'Financial Years');
    }
}
