@props(['routes', 'icon', 'label'])

@php
    $isActive = false;
    foreach ($routes as $route) {
        if (request()->routeIs($route)) {
            $isActive = true;
            break;
        }
    }
@endphp

<div x-data="{ open: {{ $isActive ? 'true' : 'false' }} }">
    <button @click="open = !open" 
            class="flex items-center justify-between w-full px-4 py-2 text-sm font-medium text-gray-300 rounded-lg hover:bg-gray-700 hover:text-white {{ $isActive ? 'bg-gray-700 text-white' : '' }}">
        <div class="flex items-center">
            <x-admin.sidebar.icon :type="$icon" />
            {{ $label }}
        </div>
        <svg class="w-4 h-4 transition-transform" :class="{ 'rotate-180': open }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
        </svg>
    </button>
    <div x-show="open" x-transition class="ml-6 mt-2 space-y-1">
        {{ $slot }}
    </div>
</div>
